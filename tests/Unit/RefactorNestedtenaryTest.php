<?php

declare(strict_types=1);

use App\Transformers\LocaleIdTransformer;

it('refactors nested tenary', function (string $value, string $locale, int $code) {
    $countryCode = in_array($value, ['gb', 'en', 'us'], strict: true)
        ? 'en'
        : ($value == 'be'
            ? 'nl'
            : $value)
    ;
    $transformer = new LocaleIdTransformer();
    expect($countryCode)->toBe($locale);
    expect($transformer->transform($value))->toBe($code);
})->with('locales');

it('can reverse transform the locale id', function (int $code, string $locale) {
    $transformer = new LocaleIdTransformer();
    expect($transformer->reverseTransform($code))->toBe($locale);
})->with('reverse locales');

dataset('locales', [
    'gb' => ['gb', 'en', 1],
    'en' => ['en', 'en', 1],
    'us' => ['us', 'en', 1],
    'be' => ['be', 'nl', 2],
    'nl' => ['nl', 'nl', 2],
]);

dataset('reverse locales', [
    'gb' => [1, 'en'],
//    'en' => [1, 'en'],
//    'us' => [1, 'en'],
//    'be' => [2, 'nl'],
//    'nl' => [2, 'nl'],
]);
