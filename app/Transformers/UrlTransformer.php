<?php

declare(strict_types=1);

namespace App\Transformers;

final class UrlTransformer extends Transformer
{
    public function transform($value)
    {
        // TODO: Implement transform() method.
    }

    public function reverseTransform($value): void
    {
        // TODO: Implement reverseTransform() method.
    }

    public function canTransform($value): bool
    {
        // TODO: Implement canTransform() method.
    }
}
