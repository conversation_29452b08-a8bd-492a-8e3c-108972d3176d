<?php

declare(strict_types=1);

namespace App\Transformers;

use Throwable;
use VIISON\AddressSplitter\AddressSplitter;
use VIISON\AddressSplitter\Exceptions\SplittingException;

final class StreetTransformer extends Transformer
{
    public function transform($value)
    {
        try {
            return data_get(AddressSplitter::splitAddress($value), 'streetName');
        } catch (SplittingException $e) {
            if ($e->getCode() === 1) {
                return $value;
            }
        } catch (Throwable) {
            return null;
        }

        return null;
    }

    public function reverseTransform($value): void
    {
        // TODO: Implement reverseTransform() method.
    }

    public function canTransform($value): bool
    {
        return is_string($value);
    }
}
