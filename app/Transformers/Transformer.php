<?php

namespace App\Transformers;

use App\Contracts\TransformerContract;
use App\Enums\TransformDirection;

abstract class Transformer implements TransformerContract
{
    public function __construct(
        protected TransformDirection $direction = TransformDirection::INBOUND,
    ) {
        //
    }

    abstract public function transform($value);

    abstract public function reverseTransform($value);

    abstract public function canTransform($value): bool;
}
