<?php

declare(strict_types=1);

namespace App\Transformers;

final class LocaleIdTransformer extends Transformer
{
    public function transform($value)
    {
        $countryCode = strtolower($value);

        $countryMappings = [
            'gb' => 'en',
            'en' => 'en',
            'us' => 'en',
            'nl' => 'nl',
            'be' => 'nl',
        ];
        $localeCode = $countryMappings[$countryCode];

        $supportedLocales = ['us', 'nl', 'de', 'en'];

        if (! in_array($localeCode, $supportedLocales, strict: true)) {
            return config('locales.en.id');
        }

        return config('locales.' . $localeCode . '.id');
    }

    public function reverseTransform($value)
    {
        $data = array_values(config('locales'));
        $column = array_column($data, 'nl');
        $x = array_search($column, $data, true);
        dump(compact('value', 'data', 'x', 'column'));

        return '';
    }

    public function canTransform($value): bool
    {
        return is_string($value);
    }
}
