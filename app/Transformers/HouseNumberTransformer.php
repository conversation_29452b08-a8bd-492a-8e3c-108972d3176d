<?php

declare(strict_types=1);

namespace App\Transformers;

use Throwable;
use VIISON\AddressSplitter\AddressSplitter;

final class HouseNumberTransformer extends Transformer
{
    public function transform($value)
    {
        try {
            return data_get(AddressSplitter::splitAddress($value), 'houseNumberParts.base');
        } catch (Throwable) {
            return null;
        }
    }

    public function reverseTransform($value): void
    {
        // TODO: Implement reverseTransform() method.
    }

    public function canTransform($value): bool
    {
        return is_string($value);
    }
}
