<?php

declare(strict_types=1);

namespace App\Transformers;

use Throwable;
use VIISON\AddressSplitter\AddressSplitter;

final class HouseNumberAdditionTransformer extends Transformer
{
    public function transform($value): ?string
    {
        try {
            $segments = AddressSplitter::splitAddress($value);

            return trim(implode(' ', [
                $segments['houseNumberParts']['extension'] ?? '',
                $segments['additionToAddress2'] ?? '',
            ]));

        } catch (Throwable) {
            return null;
        }
    }

    public function reverseTransform($value): void
    {
        // TODO: Implement reverseTransform() method.
    }

    public function canTransform($value): bool
    {
        return is_string($value);
    }
}
